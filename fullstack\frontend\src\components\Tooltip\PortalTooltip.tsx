import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface PortalTooltipProps {
  children: React.ReactNode;
  content: React.ReactNode;
  className?: string;
  delay?: number;
}

const PortalTooltip: React.FC<PortalTooltipProps> = ({
  children,
  content,
  className = '',
  delay = 200
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const updatePosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
      const scrollY = window.pageYOffset || document.documentElement.scrollTop;
      
      setPosition({
        x: rect.left + rect.width / 2 + scrollX,
        y: rect.top + scrollY
      });
    }
  };

  const handleMouseEnter = () => {
    updatePosition();
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (isVisible) {
        updatePosition();
      }
    };

    const handleResize = () => {
      if (isVisible) {
        updatePosition();
      }
    };

    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isVisible]);

  const tooltip = isVisible ? (
    <div
      className={`fixed px-4 py-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl pointer-events-none min-w-[280px] transition-opacity duration-200 ${className}`}
      style={{
        left: position.x,
        top: position.y - 8, // 8px above the trigger
        transform: 'translateX(-50%) translateY(-100%)',
        zIndex: 99999999,
        opacity: isVisible ? 1 : 0
      }}
    >
      {content}
      {/* Tooltip arrow */}
      <div
        className="absolute border-4 border-transparent border-t-gray-900"
        style={{
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)'
        }}
      />
    </div>
  ) : null;

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={className}
      >
        {children}
      </div>
      {tooltip && createPortal(tooltip, document.body)}
    </>
  );
};

export default PortalTooltip;
